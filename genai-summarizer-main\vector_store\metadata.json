{"documents": [{"id": "e8c54aebbca6d2bbcb6852c076ba3caf", "file_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4a1zlgbz.pdf", "chunk_count": 1, "word_count": 299, "char_count": 2157, "metadata": {"file_name": "tmp4a1zlgbz.pdf", "file_size": 72376, "file_type": ".pdf", "word_count": 299, "sentence_count": 19, "paragraph_count": 12, "avg_words_per_sentence": 15.736842105263158, "avg_sentences_per_paragraph": 1.5833333333333333, "headers": ["## H<PERSON><PERSON><PERSON>HU SINGH", "## EDUCATION", "## SKILLS", "## PROJECTS", "## ACHIEVEMENTS", "## INTERESTS"], "section_count": 6, "key_terms": ["learning", "development", "malaria", "frontend", "react", "nextjs", "android", "projects", "developed", "website"], "document_type": "academic_paper", "filename": "<PERSON><PERSON><PERSON> resume.pdf", "upload_time": "2025-07-13 18:00:54"}}], "chunks": [{"text": "## HIMANSHU SINGH +91-0000000000 | <EMAIL> | linkedin.com/in/himanshu-singh/ ## EDUCATION Noida Institute of Engineering and Technology, Greater Noida Jun 2022–Present B.Tech in Computer Science and Engineering (Data Science Specialization) 3rd Year (Ongoing) ## SKILLS Programming Languages: Python, C, C++, Java, JavaScript, HTML, CSS Frontend Development: React, React TypeScript, Next.js AI/ML: Machine Learning, Deep Learning, NLP, Neural Networks, Generative AI Creative Tools: Blender, Unreal Engine Other: Data Visualization, Web Development, Android Development ## PROJECTS AI-Based Learning Platform (OminiLearn) Jan 2024–Present • Developed an AI-powered learning platform to enhance education through intelligent recommendations and automation. • Utilized Next.js for the frontend, integrated with a backend for personalized learning experiences, and leveraged AI models for content recommendations. Portfolio Website Dec 2023 • Designed and developed a personal portfolio website to showcase projects and skills. • Built using Next.js, React, and TypeScript, with a focus on responsive design and modern UI/UX principles. Malaria Detection Using Machine Learning Aug 2023 • Created an ML model to detect malaria from medical images with high accuracy. • Used Python, TensorFlow, and deep learning techniques to train the model on a dataset of cell images. Android App for Malaria Detection Sep 2023 • Developed an Android app that captures prism images of blood samples to detect malaria infection. • Integrated the ML model for real-time analysis, providing users with instant results on infection status. ## ACHIEVEMENTS • Currently working on deploying a multi-feature website on Vercel, integrating frontend and backend functionalities. • Actively contributing to open-source projects in AI and web development on GitHub. • Participated in coding competitions and hackathons, focusing on AI/ML and web development solu- tions. • Learning and experimenting with 3D modeling and game development using Blender and Unreal Engine. ## INTERESTS 3D Modeling & Game Development, Photography & Editing, Video Editing, Reading", "doc_id": "e8c54aebbca6d2bbcb6852c076ba3caf", "chunk_idx": 0, "metadata": {"filename": "<PERSON><PERSON><PERSON> resume.pdf", "upload_time": "2025-07-13 18:00:54", "chunk_num": 1}}], "doc_chunk_map": {"e8c54aebbca6d2bbcb6852c076ba3caf": [0]}}